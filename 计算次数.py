import json
import pymongo
import pandas as pd
from typing import List, Dict, Any
import ast

def load_local_json(file_path: str) -> Dict[str, Any]:
    """加载本地JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        # 处理MongoDB格式的JSON（包含ObjectId和NumberInt）
        content = content.replace('ObjectId("', '"ObjectId_').replace('")', '"')
        content = content.replace('NumberInt(', '').replace(')', '')
        return json.loads(content)

def connect_mongodb() -> pymongo.MongoClient:
    """连接MongoDB数据库"""
    client = pymongo.MongoClient(
        host='**************',
        port=27017,
        username='root',
        password='root',
        authSource='admin'
    )
    return client

def process_args(args_str: str) -> List[List[int]]:
    """处理参数字符串，转换为参数列表"""
    args_list = ast.literal_eval(args_str)
    processed_args = []

    for arg in args_list:
        if len(arg) == 5:  # 如果有5个数字，去掉最后一个-1
            processed_args.append(arg[:4])
        else:
            processed_args.append(arg)

    return processed_args

def query_trade_data(db, product_name: str, args: List[int]) -> int:
    """查询交易数据，返回交易次数"""
    # 构造查询名称：商品名 + L9_15
    query_name = f"{product_name}L9_15"
    args_str = str(args)

    # 在product集合中查询
    collection = db['product']
    result = collection.find_one({
        'name': query_name,
        'args': args_str
    })

    if result:
        return result.get('number', 0)
    else:
        print(f"未找到数据: {query_name}, args: {args_str}")
        return 0

def main():
    """主函数"""
    # 1. 加载本地JSON文件
    print("正在加载本地JSON文件...")
    local_data = load_local_json('参数.Json')

    # 2. 连接MongoDB
    print("正在连接MongoDB数据库...")
    client = connect_mongodb()
    db = client['Dashboard']

    # 3. 准备结果数据
    results = []

    # 4. 处理每个商品
    tradelist = local_data['tradelist']

    for product_name, product_data in tradelist.items():
        print(f"正在处理商品: {product_name}")

        # 获取参数列表和手数
        args_str = product_data['args']
        hand_count = product_data['number']

        # 处理参数
        processed_args = process_args(args_str)

        # 查询每个参数的交易次数
        for i, args in enumerate(processed_args):
            trade_count_per_hand = query_trade_data(db, product_name, args)
            total_trade_count = trade_count_per_hand * hand_count

            results.append({
                '商品名称': product_name,
                '参数序号': i + 1,
                '参数': str(args),
                '每手交易次数': trade_count_per_hand,
                '手数': hand_count,
                '总交易次数': total_trade_count
            })

    # 5. 生成Excel报告
    print("正在生成Excel报告...")
    df = pd.DataFrame(results)

    # 保存到Excel文件
    output_file = '交易次数统计.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')

    print(f"报告已生成: {output_file}")
    print(f"共处理了 {len(results)} 条记录")

    # 显示前几行数据
    print("\n前10行数据预览:")
    print(df.head(10).to_string(index=False))

    # 关闭数据库连接
    client.close()

if __name__ == "__main__":
    main()