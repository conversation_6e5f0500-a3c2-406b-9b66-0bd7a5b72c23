import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import norm

# 数据准备（2000N量程2015-2024年数据）
years = np.arange(2015, 2025)
deviations = [2.2, -0.6, 1.5, 1.5, -0.4, -0.5, 1.0, 2.0, 2.0, 2.5]
mu, sigma = np.mean(deviations), np.std(deviations, ddof=1)
sigma = 1.23
# 生成正态曲线
x = np.linspace(mu-4*sigma, mu+4*sigma, 200)
y = norm.pdf(x, mu, sigma)

# 中文显示设置
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建图表
plt.figure(figsize=(12, 6))
plt.plot(x, y, 'b-', lw=2, label=f'正态分布 (μ={mu:.2f}N, σ={sigma:.2f}N)')

# 优化标注位置（防止重叠）
label_positions = {
    2015: (25, 15),    # 右上
    2016: (35, -25),   # 左上（提高10像素）
    2017: (25, -15),   # 右下
    2018: (-25, -25),  # 左下
    2019: (35, 5),     # 右中上
    2020: (-35, 15),   # 左中上
    2021: (-35, -5),    # 右中下
    2022: (-35, -15),  # 左中下
    2023: (15, 25),    # 右上远
    2024: (-15, 35)    # 左上远
}

for yr, dev in zip(years, deviations):
    plt.scatter(dev, norm.pdf(dev, mu, sigma), color='red', s=80, zorder=5)
    plt.annotate(f'{yr}',
                xy=(dev, norm.pdf(dev, mu, sigma)),
                xytext=label_positions[yr],
                textcoords='offset points',
                fontsize=9,
                bbox=dict(boxstyle='round', fc='yellow', alpha=0.7),
                arrowprops=dict(arrowstyle='->',
                              linewidth=0.8,
                              shrinkA=0,
                              shrinkB=5))

# 统计区间标记
plt.axvline(mu, color='gray', ls='--', label='均值')
plt.axvspan(mu-sigma, mu+sigma, color='blue', alpha=0.15, label='μ±1σ (68.27%)')
plt.axvspan(mu-2*sigma, mu+2*sigma, color='blue', alpha=0.08, label='μ±2σ (95.45%)')
plt.axvspan(mu-3*sigma, mu+3*sigma, color='blue', alpha=0.03, label='μ±3σ (99.73%)')

# 图表装饰
plt.title('2000N量程偏差值正态分布（2015-2024年）', fontsize=14, y=1.05)
plt.xlabel('偏差值 (N)', fontsize=12)
plt.ylabel('概率密度', fontsize=12)
plt.legend(loc='upper right')
plt.grid(alpha=0.3)
plt.tight_layout()
plt.show()